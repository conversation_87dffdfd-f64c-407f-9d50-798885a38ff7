"""
AI service for generating follow-up questions using Google Gemini API.
"""
import os
import google.generativeai as genai
from typing import List, Optional
import streamlit as st


class AIService:
    """Handles AI operations using Google Gemini API."""
    
    def __init__(self):
        """Initialize Gemini AI client."""
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            st.error("GEMINI_API_KEY not found in environment variables")
            return
        
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')
    
    def generate_followup_questions(self, prompt_context: str, question: str, answer: str, num_questions: int = 3) -> List[str]:
        """
        Generate follow-up questions based on the prompt context, original question, and user's answer.
        
        Args:
            prompt_context: The original prompt/context
            question: The original question
            answer: The user's answer
            num_questions: Number of follow-up questions to generate
            
        Returns:
            List of generated follow-up questions
        """
        try:
            # Construct the prompt for Gemini
            ai_prompt = f"""
            Based on the following context, original question, and user's answer, generate {num_questions} thoughtful follow-up questions that would help dive deeper into the topic or clarify the user's response.

            Context/Prompt: {prompt_context}

            Original Question: {question}

            User's Answer: {answer}

            Please generate {num_questions} follow-up questions that are:
            1. Relevant to the user's answer
            2. Help explore the topic more deeply
            3. Encourage further reflection or clarification
            4. Are open-ended and thought-provoking

            Format your response as a numbered list with each question on a new line.
            """
            
            response = self.model.generate_content(ai_prompt)
            
            if response.text:
                # Parse the response to extract individual questions
                questions = self._parse_questions_from_response(response.text)
                return questions[:num_questions]  # Ensure we don't exceed the requested number
            else:
                st.warning("No response generated from AI service")
                return []
                
        except Exception as e:
            st.error(f"Error generating follow-up questions: {str(e)}")
            return []
    
    def _parse_questions_from_response(self, response_text: str) -> List[str]:
        """
        Parse the AI response to extract individual questions.
        
        Args:
            response_text: Raw response from Gemini
            
        Returns:
            List of parsed questions
        """
        questions = []
        lines = response_text.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if line:
                # Remove numbering (1., 2., etc.) and clean up the question
                if line[0].isdigit() and '.' in line:
                    question = line.split('.', 1)[1].strip()
                elif line.startswith('-') or line.startswith('*'):
                    question = line[1:].strip()
                else:
                    question = line
                
                if question and question.endswith('?'):
                    questions.append(question)
        
        return questions
    
    def generate_batch_followups(self, question_answer_pairs: List[dict], prompt_context: str) -> dict:
        """
        Generate follow-up questions for multiple question-answer pairs.
        
        Args:
            question_answer_pairs: List of dicts with 'question_id', 'question_text', 'answer_text'
            prompt_context: The original prompt context
            
        Returns:
            Dictionary mapping question_id to list of follow-up questions
        """
        results = {}
        
        for pair in question_answer_pairs:
            if pair.get('answer_text') and pair['answer_text'].strip():
                followups = self.generate_followup_questions(
                    prompt_context=prompt_context,
                    question=pair['question_text'],
                    answer=pair['answer_text']
                )
                results[pair['question_id']] = followups
            else:
                results[pair['question_id']] = []
        
        return results
